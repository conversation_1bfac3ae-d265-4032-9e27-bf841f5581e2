import xml.etree.ElementTree as ET
import pandas as pd
import numpy as np
from scipy.optimize import minimize
from tqdm import tqdm
import time

# --- 1. 定义修正后的、范围更大的研究区域边界 ---
REGION_BOUNDARY_POLY = [
    (1700, 7100),
    (1700, 9100),
    (2850, 9100),
    (2850, 7100)
]


# --- (数据解析和聚合函数保持不变) ---

def is_vehicle_in_region(x, y, poly):
    n = len(poly)
    inside = False
    p1x, p1y = poly[0]
    for i in range(n + 1):
        p2x, p2y = poly[i % n]
        if y > min(p1y, p2y):
            if y <= max(p1y, p2y):
                if x <= max(p1x, p2x):
                    if p1y != p2y:
                        xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                    if p1x == p2x or x <= xinters:
                        inside = not inside
        p1x, p1y = p2x, p2y
    return inside


def parse_fcd_to_raw_df(fcd_file_path, region_poly):
    print(f"开始一次性解析FCD文件: {fcd_file_path}")
    tree = ET.parse(fcd_file_path)
    root = tree.getroot()
    all_data = []
    for timestep in tqdm(root.findall('timestep'), desc="处理时间步"):
        time = float(timestep.get('time'))
        for vehicle in timestep.findall('vehicle'):
            if is_vehicle_in_region(float(vehicle.get('x')), float(vehicle.get('y')), region_poly):
                all_data.append([
                    time, vehicle.get('id'), vehicle.get('type'), float(vehicle.get('speed'))
                ])
    if not all_data:
        raise ValueError("在定义的区域内没有找到任何车辆数据。")
    df = pd.DataFrame(all_data, columns=['time', 'id', 'type', 'speed'])
    print("FCD文件解析完成。")
    return df


def aggregate_data(raw_df, time_step):
    print(f"\n--- 正在按 {time_step} 秒周期进行聚合 ---")
    raw_df['time_group'] = (raw_df['time'] // time_step) * time_step
    agg_data = []
    for time_group, group in tqdm(raw_df.groupby('time_group'), desc=f"聚合数据(周期{time_step}s)"):
        unique_times_in_group = group['time'].unique()
        group_stats = []
        for t in unique_times_in_group:
            snapshot = group[group['time'] == t]
            cars = snapshot[snapshot['type'] == 'car']
            buses = snapshot[snapshot['type'] == 'bus']
            n_c, n_b = cars['id'].nunique(), buses['id'].nunique()
            v_c, v_b = (cars['speed'].mean() if n_c > 0 else 0), (buses['speed'].mean() if n_b > 0 else 0)
            group_stats.append([n_c, n_b, v_c, v_b])
        if group_stats:
            avg_stats = np.nanmean(np.array(group_stats), axis=0)
            agg_data.append([time_group, avg_stats[0], avg_stats[1], avg_stats[2], avg_stats[3]])
    result_df = pd.DataFrame(agg_data, columns=['time_step', 'n_c', 'n_b', 'v_c', 'v_b'])
    return result_df


# --- 2. 全新的、带约束的拟合函数 ---

def fit_constrained_nmfd_params(data):
    """
    使用带约束的优化算法 (scipy.optimize.minimize) 来拟合NMFD参数。
    强制 gamma >= 0, alpha <= 0, beta <= 0。
    """
    print("-" * 30)
    print("开始使用带约束的优化算法拟合3D-NMFD参数...")

    data = data[(data['n_c'] > 0) | (data['n_b'] > 0)].copy()
    if data.shape[0] < 3:  # 需要至少3个点来确定一个平面
        raise ValueError("数据点不足 (少于3个)，无法进行有效拟合。")

    X_n_c = data['n_c'].values
    X_n_b = data['n_b'].values
    y_c = data['v_c'].values
    y_b = data['v_b'].values

    # 定义目标函数：最小化误差平方和 (SSE)
    def objective_function(params, n_c, n_b, v_observed):
        gamma, alpha, beta = params
        v_predicted = gamma + alpha * n_c + beta * n_b
        error = np.sum((v_observed - v_predicted) ** 2)
        return error

    # 定义参数的边界 (Bounds)
    # gamma (截距) >= 0
    # alpha (n_c系数) <= 0
    # beta (n_b系数) <= 0
    param_bounds = [(0, None), (None, 0), (None, 0)]

    # 为优化器提供一个初始猜测值
    initial_guess = [np.mean(y_c) if len(y_c) > 0 else 10, -0.01, -0.01]

    # --- 拟合小汽车模型 ---
    print("\n正在拟合小汽车模型 (带约束)...")
    result_c = minimize(
        objective_function,
        initial_guess,
        args=(X_n_c, X_n_b, y_c),
        bounds=param_bounds,
        method='SLSQP'  # 一种支持约束的常用优化算法
    )

    if result_c.success:
        gamma_c_fit, alpha_c_fit, beta_c_fit = result_c.x
        print(f"小汽车模型拟合成功:")
        print(f"  gamma_c (自由流速度) = {gamma_c_fit:.4f}")
        print(f"  alpha_c (n_c 的影响) = {alpha_c_fit:.4f}")
        print(f"  beta_c (n_b 的影响) = {beta_c_fit:.4f}")
    else:
        print("警告: 小汽车模型优化未收敛。")
        gamma_c_fit, alpha_c_fit, beta_c_fit = np.nan, np.nan, np.nan

    # --- 拟合公交车模型 ---
    print("\n正在拟合公交车模型 (带约束)...")
    initial_guess_b = [np.mean(y_b) if len(y_b) > 0 else 8, -0.01, -0.01]
    result_b = minimize(
        objective_function,
        initial_guess_b,
        args=(X_n_c, X_n_b, y_b),
        bounds=param_bounds,
        method='SLSQP'
    )

    if result_b.success:
        gamma_b_fit, alpha_b_fit, beta_b_fit = result_b.x
        print(f"公交车模型拟合成功:")
        print(f"  gamma_b (自由流速度) = {gamma_b_fit:.4f}")
        print(f"  alpha_b (n_c 的影响) = {alpha_b_fit:.4f}")
        print(f"  beta_b (n_b 的影响) = {beta_b_fit:.4f}")
    else:
        print("警告: 公交车模型优化未收敛。")
        gamma_b_fit, alpha_b_fit, beta_b_fit = np.nan, np.nan, np.nan

    print("-" * 30)

    return {
        'gamma_c': gamma_c_fit, 'alpha_c': alpha_c_fit, 'beta_c': beta_c_fit,
        'gamma_b': gamma_b_fit, 'alpha_b': alpha_b_fit, 'beta_b': beta_b_fit
    }


# --- 主程序 ---
if __name__ == '__main__':
    FCD_FILE = 'fcd_output(lx).xml'
    AGGREGATION_PERIOD =  10 # 您可以修改这个值来测试不同周期

    try:
        # 1. 一次性解析全部FCD数据
        raw_dataframe = parse_fcd_to_raw_df(FCD_FILE, REGION_BOUNDARY_POLY)

        # 2. 对原始数据进行聚合
        aggregated_data = aggregate_data(raw_dataframe, time_step=AGGREGATION_PERIOD)

        print("\n最终聚合的数据表 (前10行):")
        print(aggregated_data.head(10))

        # 3. 使用带约束的优化算法拟合参数
        fitted_params = fit_constrained_nmfd_params(aggregated_data)

        print("\n==> 最终拟合出的 (带约束) 参数如下: <==")
        print(fitted_params)

    except FileNotFoundError:
        print(f"\n错误: 文件 '{FCD_FILE}' 未找到。")
    except ValueError as e:
        print(f"\n处理数据时发生错误: {e}")

