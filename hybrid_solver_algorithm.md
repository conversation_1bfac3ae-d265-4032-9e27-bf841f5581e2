# Table 3: Hybrid Solver Algorithm

## Hybrid solver algorithm in pseudocode

**Input:**  
- reservoir initial accumulations, nₘ(t)  
- car demand profiles, d꜀(t)  
- bus demand list  
- car average trip lengths, L꜀  
- initial Circulating bus list  

**Initialize:** Exit, Entry, and Queue bus lists = [], [], []

---

**While** t ≤ simulation period:

1. Calculate mean speed of reservoir, v(n꜀(t), n_b(t))  
2. Determine outflow capacity, p_cr(n꜀(t), n_b(t))  
3. Calculate remained spaces for each mode, Eₘ(n꜀(t), n_b(t))  
4. Determine inflow of cars, f꜀(t)  
5. For each vehicle ∈ bus demand lists:  
   - If vehicle entry time == t:  
     - Add vehicle to Queue bus list  
6. Sort Queue bus list → Based on arrival time  
7. For each vehicle ∈ Queue bus list:  
   - Confront vehicle with reservoir remained spaces for buses, E_b(n꜀(t), n_b(t))  
   - Update Entry bus list  
   - Update Circulating bus list  
   - Update Queue bus list  
8. Calculate the cumulative inflow, Fₘ(t) for each mode  
9. Determine the arrival time for each mode, t*ₘ  
10. Modify the inflow of the mode with lower delay  
11. Update Entry bus list  
12. Update Circulating bus list  
13. Update Queue bus list  
14. Sort Circulating bus list → Based on remained trip distance  
15. For each vehicle ∈ Circulating bus list:  
    - Update the remained trip distance  
    - Update exit time  
    - If vehicle exit time == t:  
      - Add vehicle to Exit bus list  
16. Calculate the outflow of buses from Exit list  
17. Obtain outflow for cars, G꜀(n꜀(t), n_b(t))  
18. If saturated traffic conditions == True:  
    - Confront obtained outflow for buses with bus outflow capacity  
    - Update Exit bus list  
    - Update Circulating bus list  
19. Update accumulation of car, n꜀(t)  
20. Calculate the outflow, inflow, and accumulation of buses from their Exit list, Entry list, and Circulating list  
21. Save accumulation, inflow, and outflow of each mode  
22. t = t + 1  

---

**End while**  

**Output:** reservoir accumulation, inflow, and outflow profiles  
