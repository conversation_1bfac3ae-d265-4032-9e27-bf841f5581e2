import xml.etree.ElementTree as ET
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from tqdm import tqdm

# --- 1. 定义修正后的、范围更大的研究区域边界 ---
# !!! 关键步骤: 我已经扩大了此区域的范围以覆盖整个路网 !!!
# 旧范围: (1700, 7600) to (2400, 8650)
# 新范围: 显著扩大了X轴（东西向）的覆盖范围
REGION_BOUNDARY_POLY_COORDS = [
    (1700, 7100),
    (1700, 9100),
    (2850, 9100),
    (2850, 7100)
]

# --- 2. 定义三个核心交叉口的坐标和名称 ---
# 这些坐标是从您的 "NormalIntersection.net.xml" 文件中提取的。
INTERSECTIONS = {
    "交叉口 1 (crossroads1)": (2254.77, 7690.52),
    "交叉口 2 (crossroads2)": (2209.53, 8154.69),
    "交叉口 3 (crossroads3)": (2186.35, 8564.22)
}


def plot_sumo_network_with_region(net_file, region_poly, intersections):
    """
    解析SUMO路网文件，绘制路网，并高亮显示分析区域和核心交叉口。
    """
    print(f"正在解析路网文件: {net_file}...")

    try:
        tree = ET.parse(net_file)
        root = tree.getroot()
    except FileNotFoundError:
        print(f"错误: 路网文件 '{net_file}' 未找到。请确保它和脚本在同一个文件夹中。")
        return
    except ET.ParseError:
        print(f"错误: 路网文件 '{net_file}' 格式有误，无法解析。")
        return

    # 创建绘图窗口
    fig, ax = plt.subplots(figsize=(12, 12))

    # 绘制所有道路（lanes）
    print("正在绘制路网...")
    lanes = root.findall(".//lane")
    for lane in tqdm(lanes, desc="绘制道路"):
        shape_str = lane.get('shape')
        if shape_str:
            # 解析坐标点
            coords = [tuple(map(float, p.split(','))) for p in shape_str.split(' ')]
            x_coords = [c[0] for c in coords]
            y_coords = [c[1] for c in coords]
            # 绘制路段
            ax.plot(x_coords, y_coords, color='gray', linewidth=0.8)

    # 绘制分析区域的红色矩形
    print("正在绘制修正后的分析区域...")
    region_patch = patches.Polygon(
        region_poly,
        linewidth=2,
        edgecolor='red',
        facecolor='red',
        alpha=0.2,  # 半透明
        label='宏观分析区域 (蓄水池)'
    )
    ax.add_patch(region_patch)

    # 标记核心交叉口
    print("正在标记核心交叉口...")
    for name, (x, y) in intersections.items():
        ax.plot(x, y, 'bo', markersize=10, label=name if list(intersections.keys()).index(name) == 0 else "")  # 只显示一个图例
        ax.text(x + 50, y, name, fontsize=12, color='blue')

    # 设置图表样式
    ax.set_aspect('equal', adjustable='box')
    ax.set_title('SUMO路网与宏观分析区域示意图 (修正版)', fontsize=16)
    ax.set_xlabel('X 坐标 (m)')
    ax.set_ylabel('Y 坐标 (m)')
    ax.grid(True, linestyle='--', alpha=0.6)

    # 创建一个统一的图例
    handles, labels = ax.get_legend_handles_labels()
    # 添加区域的图例
    handles.append(region_patch)
    labels.append('宏观分析区域 (蓄水池)')
    ax.legend(handles, labels, loc='upper right')

    print("绘图完成！")
    plt.show()


# --- 主程序 ---
if __name__ == '__main__':
    # !!! 确保您的路网文件名与此一致 !!!
    SUMO_NET_FILE = 'NormalIntersection.net.xml'

    # 设置matplotlib支持中文显示
    try:
        plt.rcParams['font.sans-serif'] = ['SimHei']  # 'SimHei' 是一种常见的中文字体
        plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    except:
        print("警告: 未找到'SimHei'字体，中文可能无法正常显示。")

    plot_sumo_network_with_region(
        net_file=SUMO_NET_FILE,
        region_poly=REGION_BOUNDARY_POLY_COORDS,
        intersections=INTERSECTIONS
    )
