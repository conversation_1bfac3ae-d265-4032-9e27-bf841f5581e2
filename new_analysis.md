代码修改总结：混合双模城市交通网络模型
本文件总结了在实现混合模型求解器时，基于论文《Macroscopic modeling of mixed bi-modal urban networks A hybrid model of accumulation- and trip-based principles》所需要进行的修改点。
1.  函数（入口生产函数）
现状： 代码中已经有一段注释 --- 代码修正处 --- 并紧随其后提供了修正后的逻辑。 修改建议： 经核对论文中的公式 (9)，当前代码中的实现 return np.where(nc <= n_cr, get_production_at_capacity(nc, nb), get_network_production(nc, nb)) 是正确的，它准确反映了在非拥堵时使用容量生产，拥堵时使用当前网络生产的逻辑。因此，这一部分无需进一步修改。
2.  类的初始化方法 
现状：
• bus_demand_list 被用作实际公交车事件的列表，但在 FIFO 逻辑中需要公交车的需求率。
• 公交车的平均行程长度 Lb 未作为独立参数传递。
修改建议：
• 添加 Lb 参数：将公交车的平均行程长度 Lb 作为 HybridModelSolver 的一个初始化参数。
• 区分公交车需求率和离散事件：在 __init__ 中，bus_demand_list 应该被替换为两个参数：一个用于 FIFO 计算的公交车需求率时间序列（例如 bus_demand_rate_profile），另一个是原始的离散公交车到达事件列表（例如 discrete_bus_arrivals）。
3.  方法 -  (公交车入口可用空间) 计算
现状： 代码在计算 Eb 时使用了 avg_Lb_in_queue。 修改建议： 论文公式 (10) 中，L_m 代表模式 m 的平均行程长度。根据数值实验描述，公交车（模式 b）的平均行程长度是恒定的 2000 (m)。因此，这里应使用 self.Lb (即 2000.0)，而不是队列中公交车的平均行程长度。
        # ...
        # Eb = (Eb_prod_share / avg_Lb_in_queue) * self.dt if avg_Lb_in_queue > 0 else 0 # 原始代码
        Eb = (Eb_prod_share / self.Lb) * self.dt if self.Lb > 0 else 0 # 修改后
        # ...
4.  方法 - FIFO 入口函数（主要修改点）
这是一个核心且复杂的修改点，涉及到正确计算等待时间 (t_star) 和调整流入量 (f_c_final, f_b_final) 以确保 FIFO 条件。
现状：
• 计算 tb_star 时，db 被错误地定义为 len(self.bus_queue_list) (公交车队列长度)，而非论文公式 (19) 所需的公交车需求率 d_b(t)。
• 在调整流入量时（论文公式 (20), (21)），用于比例计算的 db 也存在同样的错误。
• 公交车从队列进入循环列表的实际操作，在原始代码中可能发生在 FIFO 调整之前，这与论文中“根据调整后的可用空间重新计算公交车流入量”的描述不符。
修改建议：
首先，在 step 方法内部定义当前时间步的汽车和公交车需求率：
        # ...
        dc_unmodified = self.car_demand_profile[int(self.t)] * self.dt if self.t < len(self.car_demand_profile) else 0
        fc_unmodified = min(dc_unmodified, Ec) # 初步计算的汽车流入量（计数）

        # 1. 更新公交车排队列表（将新到达的公交车加入队列）
        # 将 self.bus_demand_list 改为 self.discrete_bus_arrivals
        while self.discrete_bus_arrivals and self.discrete_bus_arrivals <= self.t:
            self.bus_queue_list.append(self.discrete_bus_arrivals.pop(0))

        # 2. 初步计算公交车流入量（在 FIFO 调整之前，基于Eb的物理容量）
        buses_can_enter = 0
        temp_queue_for_fb_unmodified = self.bus_queue_list.copy()
        while buses_can_enter < Eb and temp_queue_for_fb_unmodified:
            temp_queue_for_fb_unmodified.popleft() # 模拟从队列取出
            buses_can_enter += 1
        fb_unmodified = buses_can_enter # 初步计算的公交车流入量（计数）

        fc_final, fb_final = fc_unmodified, fb_unmodified # 初始化最终流入量

        # 获取当前时间步的汽车和公交车需求率
        car_demand_rate_current = self.car_demand_profile[int(self.t)] if self.t < len(self.car_demand_profile) else 0
        bus_demand_rate_current = self.bus_demand_rate_profile[int(self.t)] if self.t < len(self.bus_demand_rate_profile) else 0

        # 仅在饱和且存在需求时进行 FIFO 调整
        if is_saturated and (car_demand_rate_current > 0 or bus_demand_rate_current > 0):
            # 计算到达时间 t*m = Fm(t) / dm(t)
            tc_star = self.Fc / car_demand_rate_current if car_demand_rate_current > 0 else float('inf')
            tb_star = self.Fb / bus_demand_rate_current if bus_demand_rate_current > 0 else float('inf')

            if tc_star < tb_star: # 汽车等待时间更短 -> 调整公交车流入量 (遵循 Eq. 20)
                if car_demand_rate_current > 0 and bus_demand_rate_current > 0:
                    # 目标公交车流入量 (基于需求比例和汽车流入量)
                    target_fb_count_from_ratio = (bus_demand_rate_current / car_demand_rate_current) * fc_unmodified
                    fb_final = int(round(min(fb_unmodified, target_fb_count_from_ratio)))
                    # 汽车流入量保持不变
                    fc_final = fc_unmodified
                else: # 某个需求率为零，无法进行比例调整
                    fb_final = 0
                    fc_final = fc_unmodified
            elif tb_star < tc_star: # 公交车等待时间更短 -> 调整汽车流入量 (遵循 Eq. 21)
                if car_demand_rate_current > 0 and bus_demand_rate_current > 0:
                    # 目标汽车流入量 (基于需求比例和公交车流入量)
                    target_fc_count_from_ratio = (car_demand_rate_current / bus_demand_rate_current) * fb_unmodified
                    fc_final = min(fc_unmodified, target_fc_count_from_ratio)
                    # 公交车流入量保持不变
                    fb_final = fb_unmodified
                else: # 某个需求率为零，无法进行比例调整
                    fc_final = 0
                    fb_final = fb_unmodified
            # else: tc_star == tb_star，无需调整，fc_final, fb_final 保持 fc_unmodified, fb_unmodified

        self.Fc += fc_final
        self.Fb += fb_final

        # 3. 根据最终确定的 fb_final，将实际公交车从队列移入循环列表
        self.bus_entry_list.clear() # 清空上一时间步的 entry_list
        entered_buses_count = 0
        # 确保只从队列中取出 fb_final 数量的公交车
        while entered_buses_count < int(round(fb_final)) and self.bus_queue_list:
            entry_time, trip_length = self.bus_queue_list.popleft()
            bus_info = {'rem_dist': trip_length, 'entry_time': self.t}
            self.bus_circulating_list.append(bus_info)
            self.bus_entry_list.append(bus_info)
            entered_buses_count += 1
说明： 这一部分是确保两种模式在饱和条件下以正确的比例进入网络的关键。它将需求率 (d_m(t)) 作为计算 t_star 和调整比例的依据，并确保最终进入的车辆数量 (fc_final, fb_final) 不超过各自未调整前的物理容量限制。
5.  方法 - 公交车流出容量计算
现状： 代码在计算 bus_outflow_capacity 时使用了 avg_Lb_in_queue。 修改建议： 同 Eb 的修改，这里也应使用 self.Lb (即 2000.0)。
        # ...
        # bus_outflow_capacity = (self.nb / total_acc if total_acc > 0 else 0) * (p_cr / avg_Lb_in_queue) * self.dt # 原始代码
        bus_outflow_capacity = (self.nb / total_acc if total_acc > 0 else 0) * (p_cr / self.Lb) * self.dt # 修改后
        # ...
6. 仿真运行设置（ 执行块）
现状：
• HybridModelSolver 的实例化参数与修改后的 __init__ 不匹配。
• 公交车需求率 (bus_demand_rate_peak, bus_demand_rate_offpeak) 未被整合成一个时间序列传递给求解器。
修改建议：
• 定义 Lb：在设置仿真参数时，明确定义 Lb。
• 创建 bus_demand_rate_profile：生成一个表示公交车需求率随时间变化的时间序列数组。
• 创建 discrete_bus_arrivals：将原始的 bus_demand_sim_list 重命名为 discrete_bus_arrivals。
• 更新求解器实例化：根据修改后的 __init__ 签名来实例化 HybridModelSolver。
# --- 4. 运行仿真 (修正后) ---
print("正在初始化并准备运行演示性仿真...")

sim_duration = 10000
car_demand_rate = 1.35 # 汽车需求率 (veh/s)
bus_demand_rate_peak = 0.1 # 高峰期公交车需求率 (veh/s)
bus_demand_rate_offpeak = 0.015 # 平峰期公交车需求率 (veh/s)
Lc = 1000.0 # 汽车平均行程长度 (m)
Lb = 2000.0 # 公交车平均行程长度 (m) # 新增

# 创建汽车需求率时间序列 (每秒的率)
car_demand_sim_profile = np.zeros(sim_duration)
car_demand_sim_profile[1000:6000] = car_demand_rate

# 创建公交车需求率时间序列 (每秒的率)，用于 FIFO 计算
bus_demand_rate_sim_profile = np.zeros(sim_duration)
bus_demand_rate_sim_profile[1000:6000] = bus_demand_rate_peak
# 在非高峰时段，保持平峰期需求率
bus_demand_rate_sim_profile[:1000] = bus_demand_rate_offpeak
bus_demand_rate_sim_profile[6000:] = bus_demand_rate_offpeak


# 创建离散公交车到达事件列表 (原始的 `bus_demand_sim_list`)
discrete_bus_arrivals = []
for t in range(sim_duration):
    rate = bus_demand_rate_peak if 1000 <= t < 6000 else bus_demand_rate_offpeak
    if np.random.rand() < rate:
        discrete_bus_arrivals.append((t, Lb)) # 使用 Lb

# 实例化求解器，传递新的参数
solver = HybridModelSolver(
    initial_nc=0,
    initial_nb=0,
    car_demand_profile=car_demand_sim_profile,
    bus_demand_rate_profile=bus_demand_rate_sim_profile, # 传递公交车需求率时间序列
    discrete_bus_arrivals=discrete_bus_arrivals,         # 传递离散公交车到达事件
    Lc=Lc,
    Lb=Lb                                                 # 传递公交车平均行程长度
)

# ... 后续仿真和绘图代码保持不变 ...

--------------------------------------------------------------------------------
总结：
主要的修改集中在 HybridModelSolver 类中，特别是如何处理公交车的需求率与离散事件，以及在饱和交通条件下精确实现 FIFO 入口函数的逻辑。通过这些修改，代码将更准确地遵循论文中描述的混合模型原理，尤其是在公交车动态和双模交互方面